import axios from 'axios';
import * as types from '../types/metamanager';
import fetch from 'node-fetch';
import { Config } from '../config';
import { Logger } from 'winston';
import { EntityManager } from 'typeorm';
import { Request, Response } from 'express';
import {
   ChatHistoryPayload,
   ChatHistoryResponse,
   MetaAutoAgentHistoryPayload,
   MetaAdsAutoAgentChat,
} from '../types/metamanager';
import { AxiosError } from 'axios';
//import { OpenAI } from 'openai';
import OpenAI from 'openai';

import { GenerateImageRequest } from '../types/metamanager';
import { AzureService } from './AzureService';
import { Readable } from 'stream';
import RunwayML from '@runwayml/sdk';
import { generateRunwayPrompt } from '../utils/meta-manager-auto-helper';

/*const openai = new OpenAI({
   apiKey: Config.OPENAI_API_KEY!,
   fetch: fetch as any,
});*/
const openai = new OpenAI({
   apiKey: Config.OPENAI_API_KEY!,
});

const client = new RunwayML({
   apiKey: Config.RUNWAYML_API_SECRET!,
});

export class MetaAdsService {
   private azureService: AzureService;
   private adAccountId: string | null = null;
   constructor(
      private logger: Logger,
      private entityManager: EntityManager,
   ) {
      this.azureService = new AzureService(logger);
   }

   private async makeApiRequest(
      url: string,
      method: string = 'GET',
      data: any = {},
      accessToken: string | null = null,
   ) {
      try {
         const response = await axios({
            method,
            url,
            params: { access_token: accessToken },
            data,
         });
         return response.data;
      } catch (err) {
         const error = err as AxiosError;

         this.logger.error('API request failed', error.message);
         throw new Error(error.message);
      }
   }
   async getMetaAccessToken(client_id: string): Promise<string | null> {
      const query = `SELECT meta_access_token FROM agents.temp_meta_ads_manager_cred WHERE client_id = $1`;
      const result = await this.entityManager.query(query, [client_id]);
      return result[0]?.meta_access_token || null;
   }
   async getAdAccountId(client_id: string): Promise<string | null> {
      const query = `SELECT ad_account_id FROM agents.temp_meta_ads_manager_cred WHERE client_id = $1`;

      const result = await this.entityManager.query(query, [client_id]);
      return result[0]?.ad_account_id || null;
   }
   async createCampaign(campaignData: types.CreateCampaignRequest) {
      const adAccountId = await this.getAdAccountId(campaignData.client_id);
      if (!adAccountId) {
         throw new Error('Ad Account ID not found for the provided client_id');
      }

      const metaAccessToken = await this.getMetaAccessToken(
         campaignData.client_id,
      );
      if (!metaAccessToken) {
         throw new Error(
            'Meta Access Token not found for the provided client_id',
         );
      }
      const url = `${Config.META_BASE_URL}/${adAccountId}/campaigns`;

      return this.makeApiRequest(url, 'POST', campaignData, metaAccessToken);
   }

   async getCampaigns() {
      const url = `${Config.META_BASE_URL}/${Config.AD_ACCOUNT_ID}/campaigns`;
      return this.makeApiRequest(url);
   }

   async createAdSet(adSetData: types.CreateAdSetRequest) {
      const adAccountId = await this.getAdAccountId(adSetData.client_id);
      if (!adAccountId) {
         throw new Error('Ad Account ID not found for the provided client_id');
      }
      const metaAccessToken = await this.getMetaAccessToken(
         adSetData.client_id,
      );
      if (!metaAccessToken) {
         throw new Error(
            'Meta Access Token not found for the provided client_id',
         );
      }

      const url = `${Config.META_BASE_URL}/${adAccountId}/adsets`;

      return await this.makeApiRequest(url, 'POST', adSetData, metaAccessToken);
   }

   async getAdSets(campaignId: string) {
      const url = `${Config.META_BASE_URL}/${campaignId}/adsets`;
      return this.makeApiRequest(url);
   }

   async createAdCreative(creativeData: types.CreateAdCreativeRequest) {
      const adAccountId = await this.getAdAccountId(creativeData.client_id);

      if (!adAccountId) {
         throw new Error('Ad Account ID not found for the provided client_id');
      }
      const metaAccessToken = await this.getMetaAccessToken(
         creativeData.client_id,
      );
      if (!metaAccessToken) {
         throw new Error(
            'Meta Access Token not found for the provided client_id',
         );
      }
      const url = `${Config.META_BASE_URL}/${adAccountId}/adcreatives`;
      return this.makeApiRequest(url, 'POST', creativeData, metaAccessToken);
   }

   async createAd(adData: types.CreateAdRequest) {
      const adAccountId = await this.getAdAccountId(adData.client_id);
      if (!adAccountId) {
         throw new Error(
            'Ad Account ID not found for the provided client_id--',
         );
      }
      const metaAccessToken = await this.getMetaAccessToken(adData.client_id);
      if (!metaAccessToken) {
         throw new Error(
            'Meta Access Token not found for the provided client_id',
         );
      }
      const url = `${Config.META_BASE_URL}/${adAccountId}/ads`;
      return this.makeApiRequest(url, 'POST', adData, metaAccessToken);
   }

   async getAdStatus(adId: string) {
      const url = `${Config.META_BASE_URL}/${adId}`;
      return this.makeApiRequest(url);
   }

   async deleteAd(adId: string) {
      const url = `${Config.META_BASE_URL}/${adId}`;
      return this.makeApiRequest(url, 'DELETE');
   }
   async summarizeCampaign(payload: any): Promise<string> {
      const prompt = `
   You are an expert marketing strategist.
   
   Given the following campaign data in JSON format, generate a clear, concise, and professionally structured summary with the following **exact** sections:
   
   ---
   
   **Campaign Overview:**
   Goal: [Clearly describe the campaign's goal and website or business name]
   Target Audience: [Describe demographics, age, interests, behavior]
   Key Messages: [Main selling points, tone, themes, or USPs]
   Content Pillars: [Key themes or ideas the content should focus on]
   
   **Advertising Platform Recommendation:**
   Based on the data, recommend the best platform(s) (e.g., Meta Ads, Google Ads) for the budget, along with a short justification.
   
   ---
   
   **Campaign JSON:**
   ${JSON.stringify(payload, null, 2)}
   
   ---
   
   Format your response using markdown-style headings and bold labels, exactly as shown above.
   `;

      const completion = await openai.chat.completions.create({
         model: 'gpt-4o-mini',
         messages: [
            {
               role: 'system',
               content:
                  'You summarize and explain ad campaign data in structured natural language.',
            },
            {
               role: 'user',
               content: prompt,
            },
         ],
         temperature: 0.5,
      });

      return completion.choices[0].message.content?.trim() || '';
   }

   async updateChatHistory(
      payload: ChatHistoryPayload,
   ): Promise<{ response: ChatHistoryResponse }> {
      try {
         const checkQuery = `
                SELECT 1 FROM ${Config.DB_Postgres_SCHEMA}.tmp_meta_ads_chat_history WHERE chat_id = $1
            `;

         const existingChat = await this.entityManager.query(checkQuery, [
            payload.chat_id,
         ]);

         if (existingChat.length > 0) {
            // Update existing record
            const updateQuery = `
                    UPDATE ${Config.DB_Postgres_SCHEMA}.tmp_meta_ads_chat_history
                    SET chat_history = $1, chat_level = $2, campaign_details = $3,
                        adset_details = $4, ad_creative_details = $5, ad_details = $6,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE chat_id = $7
                    RETURNING *;
                `;

            const updatedChat = await this.entityManager.query(updateQuery, [
               payload.chat_history,
               payload.chat_level,
               payload.campaign_details,
               payload.adset_details,
               payload.ad_creative_details,
               payload.ad_details,
               payload.chat_id,
            ]);

            return {
               response: {
                  status: 'Success',
                  message: 'Chat history updated successfully',
                  data: updatedChat[0],
               },
            };
         } else {
            // Insert new record
            const insertQuery = `
                    INSERT INTO ${Config.DB_Postgres_SCHEMA}.tmp_meta_ads_chat_history
                    (chat_name, chat_history, chat_level, campaign_details, adset_details, ad_creative_details, ad_details, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
                    RETURNING *;
                `;

            const newChat = await this.entityManager.query(insertQuery, [
               payload.chat_name,
               payload.chat_history,
               payload.chat_level,
               payload.campaign_details,
               payload.adset_details,
               payload.ad_creative_details,
               payload.ad_details,
            ]);

            return {
               response: {
                  status: 'Success',
                  message: 'Chat history inserted successfully',
                  data: newChat[0],
               },
            };
         }
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'updateChatHistory',
         });
         throw new Error(error.message);
      }
   }

   async fetchAllChatHistory(): Promise<{ response: ChatHistoryResponse }> {
      try {
         const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.tmp_meta_ads_chat_history`;
         const result = await this.entityManager.query(query);

         return {
            response: {
               status: 'Success',
               message: 'Chat history fetched successfully',
               data: result,
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'fetchAllChatHistory',
         });
         throw new Error(error.message);
      }
   }
   async saveAutoAgentHistory(
      payload: MetaAutoAgentHistoryPayload,
   ): Promise<{ response: ChatHistoryResponse }> {
      try {
         const {
            client_id,
            user_id,
            session_id,
            chat_id,
            user_query,
            final_response,
            campaign_details,
            adset_details,
            adset_data,
            ad_creative_id,
            ad_id,
            summary_content,
            streaming_messages,
            step_statuses,
         } = payload;

         const query = `
            INSERT INTO ${Config.DB_Postgres_AGENTS_SCHEMA}.meta_ads_auto_agent_chat (
               client_id,
               user_id,
               session_id,
               chat_id,
               user_query,
               final_response,
               campaign_details,
               adset_details,
               adset_data,
               ad_creative_id,
               ad_id,
               summary_content,
               streaming_messages,
               step_statuses
            )
            VALUES (
               $1, $2, $3, $4, $5,
               $6, $7::jsonb, $8::jsonb, $9::jsonb,
               $10, $11, $12, $13::jsonb, $14::jsonb
            )
            ON CONFLICT (chat_id) DO UPDATE SET
               final_response = EXCLUDED.final_response,
               campaign_details = EXCLUDED.campaign_details,
               adset_details = EXCLUDED.adset_details,
               adset_data = EXCLUDED.adset_data,
               ad_creative_id = EXCLUDED.ad_creative_id,
               ad_id = EXCLUDED.ad_id,
               summary_content = EXCLUDED.summary_content,
               streaming_messages = EXCLUDED.streaming_messages,
               step_statuses = EXCLUDED.step_statuses,
               updated_at = CURRENT_TIMESTAMP;
         `;

         await this.entityManager.query(query, [
            client_id,
            user_id,
            session_id,
            chat_id,
            user_query,
            final_response,
            campaign_details ? JSON.stringify(campaign_details) : null,
            adset_details ? JSON.stringify(adset_details) : null,
            adset_data ? JSON.stringify(adset_data) : null,
            ad_creative_id ?? null,
            ad_id ?? null,
            summary_content ?? null,
            streaming_messages ? JSON.stringify(streaming_messages) : null,
            step_statuses ? JSON.stringify(step_statuses) : null,
         ]);

         return {
            response: {
               status: 'Success',
               message: 'Chat history inserted successfully',
            },
         };
      } catch (err) {
         const error = err as Error;
         throw new Error(`Failed to insert chat history: ${error.message}`);
      }
   }

   async fetchAllAutoAgentHistory(
      client_id: string,
      user_id: string,
   ): Promise<MetaAdsAutoAgentChat[]> {
      const query = `
        SELECT *
        FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.meta_ads_auto_agent_chat
        WHERE client_id = $1 AND user_id = $2
        ORDER BY id DESC
      `;
      const result = await this.entityManager.query(query, [
         client_id,
         user_id,
      ]);

      return result.map((row: any) => ({
         ...row,
         campaign_details: row.campaign_details ?? null,
         adset_details: row.adset_details ?? null,
         adset_data: row.adset_data ?? null,
         streaming_messages: row.streaming_messages ?? null,
         step_statuses: row.step_statuses ?? null,
      }));
   }
   async fetchAutoAgentHistoryBySession(
      client_id: string,
      user_id: string,
      session_id: string,
   ): Promise<MetaAdsAutoAgentChat[]> {
      const query = `
        SELECT *
        FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.meta_ads_auto_agent_chat
        WHERE client_id = $1 AND user_id = $2 AND session_id = $3
        ORDER BY id ASC
      `;
      const result = await this.entityManager.query(query, [
         client_id,
         user_id,
         session_id,
      ]);

      return result.map((row: any) => ({
         ...row,
         campaign_details: row.campaign_details ?? null,
         adset_details: row.adset_details ?? null,
         adset_data: row.adset_data ?? null,
         streaming_messages: row.streaming_messages ?? null,
         step_statuses: row.step_statuses ?? null,
      }));
   }

   async generateCreativeImage(
      creative_desc: types.creativeImagePayload,
   ): Promise<types.creativeImageResponse> {
      if (!creative_desc?.caption || !creative_desc?.description) {
         throw new Error('caption and description must be provided');
      }

      const { caption, description } = creative_desc;

      /* const prompt = `Create a clean, high-quality ad image of ${caption}, shown fully and clearly in a modern studio or lifestyle setting. Highlight ${description} using natural lighting and soft shadows to enhance realism. Ensure the product is sharp, centered, and visually appealing. Optionally include a minimal CTA like 'Shop Now'. The style should match polished Instagram or Facebook ad creatives.`;*/
      const prompt = await generateRunwayPrompt(caption, description);

      try {
         const task = await client.textToImage
            .create({
               model: Config.RUNWAYML_MODEL as 'gen4_image',
               promptText: prompt,
               ratio: '1080:1080',
            })
            .waitForTaskOutput();

         const imageUrl = task.output?.[0];
         if (!imageUrl) throw new Error('No image output returned');

         return {
            imageUrl,
            promptUsed: prompt,
         };
      } catch (error) {
         console.error('Failed to generate image:', error);
         throw new Error('Runway image generation failed');
      }
   }

   /*async uploadChatGptImageToAzure(imageUrl: string): Promise<string> {
      if (!imageUrl) throw new Error('No image URL provided');

      const response = await fetch(imageUrl);
      if (!response.ok) throw new Error('Failed to fetch image from URL');
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      const fileName = imageUrl.split('/').pop() || `image_${Date.now()}.png`;
      const file: Express.Multer.File = {
         fieldname: 'media',
         originalname: fileName,
         encoding: '7bit',
         mimetype: response.headers.get('content-type') || 'image/png',
         size: buffer.length,
         buffer,
         stream: Readable.from(buffer),
         destination: '',
         filename: fileName,
         path: '',
      };

      const uri = await this.azureService.encodeBlob(file);
      return uri;
   }*/
}
